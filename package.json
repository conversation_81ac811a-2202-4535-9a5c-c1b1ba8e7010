{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "cross-env NODE_ENV=development tsx server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "cross-env NODE_ENV=production node dist/index.js", "check": "tsc"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.29", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.53.0", "@tanstack/react-query": "^5.84.1", "@types/bcrypt": "^6.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "dotenv": "^17.2.1", "embla-carousel-react": "^8.6.0", "express": "^4.21.2", "express-session": "^1.18.2", "framer-motion": "^11.18.2", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.453.0", "memorystore": "^1.6.7", "multer": "^2.0.2", "nanoid": "^5.1.5", "next-themes": "^0.4.6", "passport": "^0.7.0", "passport-local": "^1.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.62.0", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.9", "recharts": "^2.15.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.6", "vaul": "^1.1.2", "wouter": "^3.7.1", "ws": "^8.18.3", "zod": "^3.25.76", "zod-validation-error": "^3.5.3"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@types/express": "4.17.21", "@types/express-session": "^1.18.2", "@types/node": "20.16.11", "@types/passport": "^1.0.17", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/ws": "^8.18.1", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.21", "cross-env": "^10.0.0", "esbuild": "^0.25.8", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "tsx": "^4.20.3", "typescript": "5.6.3", "vite": "^5.4.19"}, "optionalDependencies": {"bufferutil": "^4.0.9"}}