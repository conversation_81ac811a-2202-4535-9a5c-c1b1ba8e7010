Project Title: Full-Stack Development of a Luxury Stone & Ceramic Digital Showroom

Project Overview:
We are seeking a skilled full-stack developer to create a high-end, modern, and visually stunning digital showroom for our stone and ceramic business. The website's primary purpose is to showcase our product collection in a luxurious and professional manner, serving as a portfolio to attract high-value clients.

Core Requirements & Key Features:

1. Design & Aesthetics:

Luxury Feel: The overall design must be modern, clean, and sophisticated, evoking a sense of luxury and premium quality.

Color Palette: The color scheme should be elegant and professional, reflecting the high-end nature of our products (e.g., using neutral tones like charcoal, beige, and white, with metallic or rich accent colors).

User Experience (UX): The user journey should be seamless, intuitive, and visually engaging. The focus is on creating an impressive visual experience.

2. Front-End Development (Client-Facing Showcase):

Non-E-commerce Showcase: The website is strictly for showcasing products. There should be no shopping cart, online payment, or e-commerce functionality.

Product Catalog:

An elegant main page to display featured products or categories.

An intuitive and well-organized product listing page.

Product Detail Pages: Each product must have a dedicated page featuring:

A unique Product Code (e.g., "MBL-012").

A gallery for multiple high-resolution product images.

A detailed description area.

Advanced Categorization: With a portfolio of ~80 products, we need an efficient categorization and filtering system. Users should be able to easily browse products by categories (e.g., Marble, Granite, Travertine, Ceramic Tiles) and potentially filter by other attributes.

Multi-Language Support: The website must fully support two languages: English and Arabic, with an easily accessible language switcher.

Responsive Design: The site must be fully responsive and optimized for a flawless experience on desktops, tablets, and mobile devices.

3. Back-End Development (Admin Panel):

Simple & Secure Admin Panel: A user-friendly and secure back-end for content management.

Product Management (CRUD Operations): The site administrator must be able to easily:

Create: Add new products with their codes, descriptions, and images.

Read: View the list of all products.

Update: Edit existing product details.

Delete: Remove products from the showcase.

Category Management: The ability to create, edit, and delete product categories and assign products to them.

Basic Site Settings: Simple controls to update general website information (e.g., contact details, social media links, etc.).

Summary of Deliverables:
A fully functional, full-stack website that acts as a luxurious digital showroom. The final product should be bilingual (English/Arabic), fully responsive, and managed through a simple yet powerful admin panel for product and category management.